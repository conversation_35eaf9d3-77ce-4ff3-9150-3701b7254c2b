import { colours } from '@/common/colours'
import CustomIconButton from '@/common/components/CustomIconButton'
import { useCallback } from 'react'
import FirstPageIcon from '@mui/icons-material/FirstPage'
import LastPage from '@mui/icons-material/LastPage'
import { useStateWithStorage } from '@/common/storage.service'

interface MinimizeSidebarIconButtonProps {
    anchorElement: HTMLElement | undefined | null
}

export default function useMinimizeSidebarIconButton({ anchorElement }: MinimizeSidebarIconButtonProps) {
    const [sidebarIsMinimized, setSidebarIsMinimized] = useStateWithStorage(
        'content-editor-sidebar-is-minimized',
        false
    )

    const MinimizeSidebarIconButton = useCallback(() => {
        return (
            <CustomIconButton
                size='small'
                round
                onClick={() => setSidebarIsMinimized(!sidebarIsMinimized)}
                sx={{
                    fontSize: '4px',
                    backgroundColor: colours.base_blue,
                    color: colours.white,
                    border: `1px solid ${colours.off_white_but_darker}`,
                    zIndex: 1,
                    position: 'fixed',
                    marginTop: '10rem',
                    marginLeft: '-1rem',
                    '.MuiSvgIcon-root': {
                        fontSize: '18px'
                    }
                }}
            >
                {sidebarIsMinimized ? <LastPage /> : <FirstPageIcon />}
            </CustomIconButton>
        )
    }, [anchorElement, setSidebarIsMinimized, sidebarIsMinimized])

    return { sidebarIsMinimized, MinimizeSidebarIconButton }
}
