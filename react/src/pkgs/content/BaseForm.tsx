import { Base } from './types'
import { SiteSelectorForContentProps } from '../../common/components/selectors/SiteSelectorForContent'
import { entityScope } from '../auth/entityScope'
import { Box, FormControl, FormLabel, InputLabel, MenuItem, Select } from '@mui/material'
import { useAppContext } from '../auth/atoms'
import { PublishPeriodControl } from './editor/components/PublishPeriodControl'
import React from 'react'
import PublishStatusLabel from './editor/components/PublishStatusLabel'
import { publishStatus } from './editor/ContentEditorSaveBar'
import { PrivacyLevel } from '@/pkgs/content/PrivacyLevel'
import { SitesSelectorComponent } from '@/common/components/selectors/SitesSelectorComponent'

interface BaseFormConfig {
    moreSiteSelectorProps?: Omit<
        SiteSelectorForContentProps,
        'ContentType' | 'Selected' | 'OnChange' | 'HasError' | 'ParentSitesIDs'
    >
}

interface BaseFormProps<T extends Base> {
    value: T | undefined
    onChange: (v: T) => void
    contentType: entityScope
    errors?: Partial<Record<keyof Base, string>>
    disabledFields?: Partial<Record<keyof Base, boolean>> | boolean
    hiddenFields?: Partial<Record<keyof Base, boolean>>
    parentSitesIDs?: string[]
    config?: BaseFormConfig
}

export function BaseForm<T extends Base>({
    value,
    onChange,
    contentType,
    errors,
    parentSitesIDs,
    disabledFields,
    hiddenFields
}: BaseFormProps<T>) {
    const evaluators = useAppContext()
    if (!value) {
        value = {} as T
    }

    React.useEffect(() => {
        if (!value) {
            value = {} as T
        }
        value.Sites = !value.Sites || value.Sites.length === 0 ? evaluators.getDefaultSitesForSelectors() : value.Sites
        value.PrivacyLevel = value.PrivacyLevel === undefined ? 0 : value.PrivacyLevel
        value.DepartmentID = value.DepartmentID || evaluators.getCurrentSiteDepartmentID()
    }, [])

    const isDisabled = (field: keyof Base) => {
        if (typeof disabledFields === 'boolean') return disabledFields
        return disabledFields && disabledFields[field]
    }

    return (
        <Box display='flex' flexDirection='column' gap='16px'>
            <FormControl sx={{ width: '100%' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <FormLabel id='publish-period'>Scheduling: </FormLabel>
                    <FormLabel id='publish-status'>
                        <PublishStatusLabel publishAt={value.PublishAt} expireAt={value.ExpireAt} />
                    </FormLabel>
                </div>
                <PublishPeriodControl
                    value={{ PublishAt: value.PublishAt, ExpireAt: value.ExpireAt }}
                    onChange={(period) => {
                        value && onChange({ ...value, ...period })
                    }}
                    disabled={isDisabled('PublishAt') || isDisabled('ExpireAt')}
                />
            </FormControl>

            {!(hiddenFields && hiddenFields.Sites) && (
                <>
                    <SitesSelectorComponent
                        value={value}
                        onChange={(v) =>
                            value &&
                            onChange({
                                ...value,
                                Sites: v.Sites,
                                DepartmentID: v.DepartmentID
                            })
                        }
                        contentType={contentType}
                        disabled={isDisabled('Sites')}
                        error={errors?.Sites}
                    />
                </>
            )}

            <PrivacyLevel
                value={value?.PrivacyLevel || 0}
                onChange={(v) => value && onChange({ ...value, PrivacyLevel: v })}
                isDisabled={Boolean(isDisabled('PrivacyLevel'))}
            />
        </Box>
    )
}

export function StatusSelector({
    value,
    onChange
}: {
    value: publishStatus | '' | undefined | null
    onChange: (v: publishStatus | '') => void
}) {
    return (
        <FormControl variant={'outlined'} sx={{ width: '100%' }}>
            <InputLabel id={'structure-selector'}>Status</InputLabel>
            <Select
                value={value || ''}
                onChange={(e) => {
                    onChange(e.target.value as publishStatus | '')
                }}
                labelId='structure-selector'
                label={'Status'}
            >
                <MenuItem value={''}>Any</MenuItem>
                <MenuItem value={'draft'}>Draft</MenuItem>
                <MenuItem value={'published'}>Published</MenuItem>
                <MenuItem value={'scheduled'}>Scheduled</MenuItem>
                <MenuItem value={'expired'}>Expired</MenuItem>
            </Select>
        </FormControl>
    )
}
